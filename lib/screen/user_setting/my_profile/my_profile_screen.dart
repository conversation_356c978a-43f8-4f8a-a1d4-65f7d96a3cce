import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/text_field.dart/text_field.dart';

class MyProfileScreen extends StatefulWidget {
  const MyProfileScreen({super.key});

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  late TextEditingController _displayNameController;
  late TextEditingController _usernameController;
  late FocusNode _displayNameFocusNode;
  late FocusNode _usernameFocusNode;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController();
    _usernameController = TextEditingController();
    _displayNameFocusNode = FocusNode();
    _usernameFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _usernameController.dispose();
    _displayNameFocusNode.dispose();
    _usernameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ProfileCubit>()..getProfile(),
      child: Scaffold(
        backgroundColor: const Color(0xFFFFFFFF), // Neutrals/Neutral 50 [day]
        body: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            final user = state.userModel;
            final displayName = user?.username ?? 'Robert Fox123';
            final username = user?.username ?? '@meshii235';

            // Set initial values if not already set
            if (_displayNameController.text.isEmpty) {
              _displayNameController.text = displayName;
            }
            if (_usernameController.text.isEmpty) {
              _usernameController.text = username;
            }

            return Column(
              children: [
                // Status bar
                _buildStatusBar(),

                // Main content
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const SizedBox(height: 102),

                        // Profile header section
                        _buildProfileHeader(displayName),

                        const SizedBox(height: 170),

                        // Form fields
                        _buildFormFields(),

                        const SizedBox(height: 376),

                        // Update Profile button
                        _buildUpdateButton(),

                        const SizedBox(height: 26),

                        // Delete account option
                        _buildDeleteAccountOption(),

                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusBar() {
    return Container(
      height: 44,
      color: const Color(0xFFFFFFFF), // Sky/White
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Time
              const Text(
                '9:41',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  height: 1.0,
                  color: Color(0xFF090A0A), // Ink/Darkest
                ),
              ),

              // Right side indicators
              Row(
                children: [
                  // Mobile Signal
                  _buildMobileSignal(),
                  const SizedBox(width: 5),
                  // Wifi
                  _buildWifiIcon(),
                  const SizedBox(width: 6),
                  // Battery
                  _buildBatteryIcon(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileSignal() {
    return SizedBox(
      width: 18,
      height: 10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(width: 3, height: 4, color: const Color(0xFFDADADA)),
          Container(width: 3, height: 6, color: const Color(0xFFDADADA)),
          Container(width: 3, height: 8, color: const Color(0xFFDADADA)),
          Container(width: 3, height: 10, color: const Color(0xFF090A0A)),
        ],
      ),
    );
  }

  Widget _buildWifiIcon() {
    return const SizedBox(
      width: 15.27,
      height: 10.97,
      child: Icon(
        Icons.wifi,
        size: 15,
        color: Color(0xFF090A0A),
      ),
    );
  }

  Widget _buildBatteryIcon() {
    return SizedBox(
      width: 26.98,
      height: 13,
      child: Stack(
        children: [
          // Battery outline
          Container(
            width: 25,
            height: 13,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF090A0A).withValues(alpha: 0.35),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(2.67),
            ),
          ),
          // Battery tip
          Positioned(
            right: 0,
            top: 5,
            child: Container(
              width: 1.33,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFF090A0A).withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(1.33),
              ),
            ),
          ),
          // Battery fill
          Positioned(
            left: 2.3,
            top: 2.33,
            child: Container(
              width: 20.2,
              height: 8.33,
              decoration: BoxDecoration(
                color: const Color(0xFF090A0A),
                borderRadius: BorderRadius.circular(1.33),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(String displayName) {
    return Column(
      children: [
        // Avatar with camera icon
        Stack(
          children: [
            // Avatar
            Container(
              width: 104,
              height: 104,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(52),
                image: DecorationImage(
                  image: AssetImage(Assets.images.avatarSample.path),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            // Camera icon
            Positioned(
              right: 4,
              bottom: 3,
              child: Container(
                width: 22,
                height: 22,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C4EFF), // Primary/blue_500
                  borderRadius: BorderRadius.circular(100),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  size: 14,
                  color: Color(0xE6FFFFFF), // White transperant/White-900
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // User name
        Text(
          displayName,
          style: titleLarge.copyWith(
            color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Display name field
          TTextField(
            labelText: 'Displayname',
            textController: _displayNameController,
            focusNode: _displayNameFocusNode,
            fillColor: Colors.white,
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),

          const SizedBox(height: 16),

          // Username field
          TTextField(
            labelText: 'Username',
            textController: _usernameController,
            focusNode: _usernameFocusNode,
            fillColor: Colors.white,
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: 358,
        height: 48,
        decoration: BoxDecoration(
          color: const Color(0xFFF6F6F6), // Neutrals/Neutral 100 [day]
          borderRadius: BorderRadius.circular(48),
        ),
        child: Center(
          child: Text(
            'Update Profile',
            style: titleMedium.copyWith(
              color: const Color(0xFFAFAFAF), // Neutrals/Neutral 300 [day]
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteAccountOption() {
    return Container(
      width: 390,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // Close circle icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.cancel_outlined,
              size: 20,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),

          const SizedBox(width: 16),

          // Delete account text
          Expanded(
            child: Text(
              'Delete account',
              style: titleMedium.copyWith(
                color: const Color(0xFFD33636), // Foundation/Red/Red-600
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Arrow right icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),
        ],
      ),
    );
  }
}
